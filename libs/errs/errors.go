package errs

import (
	"digital-transformation-api/libs/apps"
	"fmt"
	"net/http"
)

type Error interface {
	Error() string
	Status() int
	Code() string
}

type Errs struct {
	ErrorCode   string `json:"code"`
	Message     string `json:"message"`
	Description string `json:"description"`
	StatusCode  int    `json:"-"`
	Version     string `json:"version"`
}

func (e *Errs) Error() string {
	return fmt.Sprintf("status: %d, code: %s", e.StatusCode, e.ErrorCode)
}

func (e *Errs) Status() int {
	return e.StatusCode
}

func (e *Errs) Code() string {
	return e.ErrorCode
}

func New(status int, code string) Error {
	return &Errs{
		StatusCode: status,
		ErrorCode:  code,
	}
}

func NewCustom(status int, code, message, description string) Error {
	return &Errs{
		StatusCode:  status,
		ErrorCode:   code,
		Message:     message,
		Description: description,
		Version:     apps.ApiVersion,
	}
}

func NewInternalError() Error {
	return &Errs{
		StatusCode: http.StatusInternalServerError,
		ErrorCode:  Err50001,
		Version:    apps.ApiVersion,
	}
}

func NewBadRequestError() Error {
	return &Errs{
		StatusCode: http.StatusBadRequest,
		ErrorCode:  Err40000,
		Version:    apps.ApiVersion,
	}
}

func NewBusinessError(code string) Error {
	return &Errs{
		StatusCode: http.StatusConflict,
		ErrorCode:  code,
		Version:    apps.ApiVersion,
	}
}

func NewExternalError() Error {
	return &Errs{
		StatusCode: http.StatusInternalServerError,
		ErrorCode:  Err50003,
		Version:    apps.ApiVersion,
	}
}
